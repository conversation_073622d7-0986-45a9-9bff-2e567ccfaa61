name: Linux Release
permissions:
  contents: write

on:
  push:
    tags:
      - '*'
      - '!*-alpha*'
jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v3
        with:
          node-version: 16
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: '>=1.18.0'
      - name: Build Backend (amd64)
        run: |
          go mod download
          go build -ldflags "-s -w -X 'lmarena2api/common.Version=$(git describe --tags)' -extldflags '-static'" -o lmarena2api

      - name: Build Backend (arm64)
        run: |
          sudo apt-get update
          sudo apt-get install gcc-aarch64-linux-gnu
          CC=aarch64-linux-gnu-gcc CGO_ENABLED=1 GOOS=linux GOARCH=arm64 go build -ldflags "-s -w -X 'lmarena2api/common.Version=$(git describe --tags)' -extldflags '-static'" -o lmarena2api-arm64

      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            lmarena2api
            lmarena2api-arm64
          draft: false
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}