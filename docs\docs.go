// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/v1/chat/completions": {
            "post": {
                "description": "OpenAI对话接口",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "OpenAI"
                ],
                "parameters": [
                    {
                        "description": "OpenAI对话请求",
                        "name": "req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.OpenAIChatCompletionRequest"
                        }
                    },
                    {
                        "type": "string",
                        "description": "Authorization API-KEY",
                        "name": "Authorization",
                        "in": "header",
                        "required": true
                    }
                ],
                "responses": {}
            }
        },
        "/v1/models": {
            "get": {
                "description": "OpenAI模型列表接口",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "OpenAI"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "description": "Authorization API-KEY",
                        "name": "Authorization",
                        "in": "header",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/common.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/model.OpenaiModelListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "common.ResponseResult": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "model.OpenAIChatCompletionRequest": {
            "type": "object",
            "properties": {
                "max_tokens": {
                    "type": "integer"
                },
                "messages": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.OpenAIChatMessage"
                    }
                },
                "model": {
                    "type": "string"
                },
                "stream": {
                    "type": "boolean"
                }
            }
        },
        "model.OpenAIChatMessage": {
            "type": "object",
            "properties": {
                "content": {},
                "role": {
                    "type": "string"
                }
            }
        },
        "model.OpenaiModelListResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.OpenaiModelResponse"
                    }
                },
                "object": {
                    "type": "string"
                }
            }
        },
        "model.OpenaiModelResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "object": {
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0.0",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "HIX-AI-2API",
	Description:      "HIX-AI-2API",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
